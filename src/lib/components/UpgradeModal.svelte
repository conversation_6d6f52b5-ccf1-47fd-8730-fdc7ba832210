<script lang="ts">
    import { goto } from '$app/navigation';
    import { onDestroy } from 'svelte';
    import { createCheckoutSession } from '$lib/services/subscriptionService';
    import { PRICING_PLANS } from '$lib/config/pricing';
    import { shouldShowPremiumContent } from '$lib/config/app';
    import PriceCard from './PriceCard.svelte';
    
    export let open = false;
    
    let loading = false;
    let error: string | null = null;
    let processingPriceId: string | null = null;
    let errorTimeout: ReturnType<typeof setTimeout> | null = null;
    
    function setError(message: string) {
        error = message;
        
        // Clear any existing timeout
        if (errorTimeout) {
            clearTimeout(errorTimeout);
        }
        
        // Auto-clear error after 8 seconds
        errorTimeout = setTimeout(() => {
            error = null;
            errorTimeout = null;
        }, 8000);
    }
    
    function clearError() {
        error = null;
        if (errorTimeout) {
            clearTimeout(errorTimeout);
            errorTimeout = null;
        }
    }
    
    onDestroy(() => {
        if (errorTimeout) {
            clearTimeout(errorTimeout);
        }
    });
    
    async function handleSubscribe(priceId: string) {
        loading = true;
        processingPriceId = priceId;
        error = null;
        
        try {
            const result = await createCheckoutSession(priceId);
            
            if (result.error) {
                setError(result.error);
            } else if (result.url) {
                // Redirect to Stripe Checkout
                window.location.href = result.url;
            } else {
                setError('Unable to start checkout process. Please try again.');
            }
        } catch (err) {
            setError('An unexpected error occurred. Please try again.');
            console.error('Subscription error:', err);
        } finally {
            loading = false;
            processingPriceId = null;
        }
    }
    
    function handleClose() {
        if (!loading) {
            open = false;
            clearError();
        }
    }
    
    function handleViewAllOptions() {
        open = false;
        goto('/pricing');
    }
</script>

{#if open && shouldShowPremiumContent()}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4">
        <!-- Backdrop -->
        <button
            class="absolute inset-0 bg-black/50 backdrop-blur-sm"
            on:click={handleClose}
            disabled={loading}
            aria-label="Close modal"
        ></button>
        
        <!-- Modal -->
        <div class="relative bg-background rounded-xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
            <!-- Header with close button -->
            <div class="sticky top-0 bg-background border-b p-6 flex justify-between items-start">
                <div>
                    <h2 class="text-2xl font-bold">Unlock Your Best Days</h2>
                    <p class="text-muted-foreground mt-1">
                        Choose a plan to access all premium features
                    </p>
                </div>
                <button
                    on:click={handleClose}
                    disabled={loading}
                    class="p-2 hover:bg-accent rounded-lg transition-colors"
                    aria-label="Close"
                >
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            
            <!-- Content -->
            <div class="p-6">
                {#if error}
                    <div class="mb-6 p-4 bg-destructive/10 text-destructive rounded-lg text-sm relative">
                        <button
                            on:click={clearError}
                            class="absolute top-3 right-3 p-1 hover:bg-destructive/20 rounded transition-colors"
                            aria-label="Dismiss error"
                        >
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                        <span class="pr-8">{error}</span>
                    </div>
                {/if}
                
                <!-- Pricing Cards Grid -->
                <div class="grid gap-6 md:grid-cols-3 mb-6">
                    {#each PRICING_PLANS as option (option.priceId)}
                        <PriceCard
                            priceId={option.priceId}
                            subInterval={option.subInterval}
                            subIntervalType={option.subIntervalType}
                            title={option.name}
                            price={option.price}
                            recommended={option.recommended}
                        >
                            <p class="text-gray-600 text-sm mb-6">{option.description}</p>
                            
                            <ul class="space-y-3 mb-6">
                                {#each option.features as feature (feature)}
                                    <li class="flex items-center text-sm text-gray-600">
                                        <svg class="w-4 h-4 text-green-500 mr-2 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                        {feature}
                                    </li>
                                {/each}
                            </ul>
                            
                            <button
                                on:click={() => handleSubscribe(option.priceId)}
                                disabled={loading}
                                class="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 {option.recommended ? 'bg-blue-600 hover:bg-blue-700' : ''}"
                            >
                                {#if loading && processingPriceId === option.priceId}
                                    <div class="flex items-center justify-center">
                                        <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                        Processing...
                                    </div>
                                {:else}
                                    Start Free Trial
                                {/if}
                            </button>
                        </PriceCard>
                    {/each}
                </div>
                
                <!-- Additional Info -->
                <div class="text-center text-sm text-muted-foreground">
                    <p class="mb-2">
                        All plans include a 7-day free trial and a 90 day money back guarantee!
                    </p>
                    <p>
                        Cancel anytime. Secure payment via Stripe.
                    </p>
                </div>
            </div>
            
            <!-- Footer -->
            <div class="sticky bottom-0 bg-background border-t p-6">
                <button
                    class="w-full px-4 py-2 border rounded-lg hover:bg-accent transition-colors text-center"
                    on:click={handleViewAllOptions}
                    disabled={loading}
                >
                    View Full Pricing Page
                </button>
            </div>
        </div>
    </div>
{/if}