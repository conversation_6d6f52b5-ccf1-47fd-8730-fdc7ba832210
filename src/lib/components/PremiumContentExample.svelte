<!--
    Example component showing how to use the global premium content flag
    This demonstrates the different helper functions available
-->
<script lang="ts">
    import { 
        shouldShowPremiumContent,
        shouldShowPremiumPromotions,
        shouldShowPricing,
        shouldShowUpgradeOptions 
    } from '$lib/config/app';
</script>

<div class="p-6 space-y-4 border rounded-lg">
    <h3 class="text-lg font-semibold">Premium Content Visibility Demo</h3>
    
    {#if shouldShowPremiumContent()}
        <div class="p-4 bg-blue-50 border border-blue-200 rounded">
            <p class="text-blue-800">✅ Premium content is visible</p>
        </div>
    {:else}
        <div class="p-4 bg-gray-50 border border-gray-200 rounded">
            <p class="text-gray-600">❌ Premium content is hidden</p>
        </div>
    {/if}
    
    {#if shouldShowPremiumPromotions()}
        <div class="p-4 bg-green-50 border border-green-200 rounded">
            <p class="text-green-800">🌟 Premium promotions are visible</p>
            <button class="mt-2 px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700">
                Upgrade Now!
            </button>
        </div>
    {:else}
        <div class="p-4 bg-gray-50 border border-gray-200 rounded">
            <p class="text-gray-600">🚫 Premium promotions are hidden</p>
        </div>
    {/if}
    
    {#if shouldShowPricing()}
        <div class="p-4 bg-purple-50 border border-purple-200 rounded">
            <p class="text-purple-800">💰 Pricing information is visible</p>
            <a href="/pricing" class="mt-2 inline-block px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700">
                View Pricing
            </a>
        </div>
    {:else}
        <div class="p-4 bg-gray-50 border border-gray-200 rounded">
            <p class="text-gray-600">💸 Pricing information is hidden</p>
        </div>
    {/if}
    
    {#if shouldShowUpgradeOptions()}
        <div class="p-4 bg-orange-50 border border-orange-200 rounded">
            <p class="text-orange-800">⬆️ Upgrade options are visible</p>
            <button class="mt-2 px-4 py-2 bg-orange-600 text-white rounded hover:bg-orange-700">
                Choose Plan
            </button>
        </div>
    {:else}
        <div class="p-4 bg-gray-50 border border-gray-200 rounded">
            <p class="text-gray-600">⬇️ Upgrade options are hidden</p>
        </div>
    {/if}
</div>
