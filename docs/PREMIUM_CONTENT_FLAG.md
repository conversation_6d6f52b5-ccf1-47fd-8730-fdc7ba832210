# Global Premium Content Flag

This document explains how to use the global `HIDE_PREMIUM_CONTENT` flag to control the visibility of all premium content and promotions throughout the app.

## Overview

The global flag allows you to hide all premium-related content in one place, including:
- Premium upgrade prompts and buttons
- Pricing pages and pricing information  
- Premium feature promotions
- Subscription-related UI elements
- Any content that promotes premium features

## Configuration

### Location
The flag is defined in `src/lib/config/app.ts`:

```typescript
// Global flag to hide all premium content and promotions throughout the app
export const HIDE_PREMIUM_CONTENT = false;
```

### Usage
To hide all premium content, simply change the flag to `true`:

```typescript
export const HIDE_PREMIUM_CONTENT = true;
```

## Helper Functions

The configuration file provides several helper functions to check premium content visibility:

```typescript
import { 
    shouldShowPremiumContent,
    shouldShowPremiumPromotions,
    shouldShowPricing,
    shouldShowUpgradeOptions 
} from '$lib/config/app';
```

### Available Functions

- `shouldShowPremiumContent()` - General premium content visibility
- `shouldShowPremiumPromotions()` - Premium upgrade prompts and promotions
- `shouldShowPricing()` - Pricing pages and pricing information
- `shouldShowUpgradeOptions()` - Upgrade buttons and links

## Implementation Examples

### Basic Usage in Components

```svelte
<script lang="ts">
    import { shouldShowPremiumPromotions } from '$lib/config/app';
</script>

{#if shouldShowPremiumPromotions()}
    <div class="upgrade-prompt">
        <h3>Upgrade to Premium!</h3>
        <button>Get Started</button>
    </div>
{/if}
```

### Hiding Entire Pages

```svelte
<script lang="ts">
    import { shouldShowPricing } from '$lib/config/app';
    import { goto } from '$app/navigation';
</script>

{#if !shouldShowPricing()}
    <div class="page-not-available">
        <h1>Page Not Available</h1>
        <button on:click={() => goto('/app')}>Go to App</button>
    </div>
{:else}
    <!-- Normal pricing page content -->
{/if}
```

## Components Already Updated

The following components have been updated to respect the global flag:

1. **PremiumPrompt.svelte** - Uses `shouldShowPremiumPromotions()`
2. **UpgradeModal.svelte** - Uses `shouldShowPremiumContent()`
3. **Free page** (`/app/free/+page.svelte`) - Uses `shouldShowPremiumPromotions()`
4. **Pricing page** (`/pricing/+page.svelte`) - Uses `shouldShowPricing()`

## Testing

A demo component is available at `src/lib/components/PremiumContentExample.svelte` that shows all the helper functions in action. You can import and use this component to test the flag behavior.

## Best Practices

1. **Use specific helper functions** - Use the most appropriate helper function for your use case
2. **Consistent implementation** - Always wrap premium content in conditional blocks
3. **Graceful degradation** - Provide alternative content or navigation when premium content is hidden
4. **Test both states** - Test your app with the flag both enabled and disabled

## Environment-Based Configuration

For different environments, you could extend this to use environment variables:

```typescript
// Example: Hide premium content in development
export const HIDE_PREMIUM_CONTENT = import.meta.env.DEV;
```

Or create environment-specific configurations as needed.
