<script lang="ts">
    import { goto } from '$app/navigation';
    import { FreeYourselfButton } from '$lib/components/FreeYourself';
    import PremiumPrompt from '$lib/components/PremiumPrompt.svelte';
    import { signOut } from '$lib/auth';
    import { shouldShowPremiumPromotions } from '$lib/config/app';

    async function handleLogout() {
        await signOut();
        goto('/');
    }
</script>

<svelte:head>
    <title>YourBestDays - Free</title>
</svelte:head>

<div class="max-w-4xl mx-auto p-5">
    {#if shouldShowPremiumPromotions()}
        <!-- Upgrade Prompt Section -->
        <div class="mb-12 text-center">
            <h1 class="text-3xl font-bold mb-4">Want Premium Features?</h1>
            <p class="text-lg text-muted-foreground mb-6">
                Your freedom towards peace, happiness and embracing <span class="font-semibold text-primary">Your Best Days</span>, in any situation, is found by seeing, remembering and taking action. Upgrade to <span class="font-semibold text-primary">Your Best Days</span> Premium to unlock personalized notifications, messages and actions focused on your goals and more.
            </p>
            <button
                on:click={() => goto('/pricing')}
                class="px-8 py-3 bg-primary text-primary-foreground font-medium rounded-lg hover:bg-primary/90 transition-colors text-lg"
            >
                Upgrade to Premium
            </button>
        </div>

        <!-- Divider -->
        <div class="w-full h-px bg-border mb-12"></div>
    {/if}
    
    <!-- Free Yourself Component -->
    <FreeYourselfButton />
    
    <!-- Logout Section -->
    <div class="mt-16 mb-8 text-center">
        <button
            on:click={handleLogout}
            class="px-6 py-2 border border-muted-foreground/30 text-muted-foreground rounded-lg hover:bg-muted/10 transition-colors"
        >
            Sign Out
        </button>
    </div>
    
    <!-- Premium Prompt -->
    <PremiumPrompt />
</div>